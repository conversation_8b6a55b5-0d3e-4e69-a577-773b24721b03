import { DBOS } from '@dbos-inc/dbos-sdk';
import express, { Request, Response } from 'express';
import { ForaChat } from './operations';
import { StreamingChatService } from './streaming';
import dotenv from 'dotenv';
import WebSocket from 'ws';
import http from 'http';
import { v4 as uuidv4 } from 'uuid';

// Load environment variables from .env file
dotenv.config();

const app = express();
app.use(express.json());

// Serve React build files (for production)
app.use(express.static('public/dist'));

// Create HTTP server
const server = http.createServer(app);

// Create WebSocket server
const wss = new WebSocket.Server({ server });
const streamingService = new StreamingChatService();

// WebSocket connection handler
wss.on('connection', (ws: WebSocket) => {
    const sessionId = uuidv4();
    console.log(`New WebSocket connection: ${sessionId}`);

    streamingService.createSession(sessionId, ws);

    ws.on('close', () => {
        console.log(`WebSocket connection closed: ${sessionId}`);
    });
});

// Traditional chat endpoint (for REPL and direct API calls)
app.post('/chat', (req: Request, res: Response) => {
    (async () => {
        try {
            const { text } = req.body;
            if (!text) {
                return res.status(400).json({ error: 'Request body must include "text"' });
            }
            const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text);
            const result = await handle.getResult();
            res.json(result);
        } catch (error) {
            DBOS.logger.error(`Error in /chat handler: ${(error as Error).message}`);
            const errorMessage = (error as Error).message;
            res.status(500).json({ error: "An internal server error occurred.", details: errorMessage });
        }
    })();
});

// Health check endpoint
app.get('/health', (req: Request, res: Response) => {
    res.json({
        status: 'ok',
        websocket_sessions: streamingService.getSessionCount(),
        timestamp: new Date().toISOString()
    });
});

// The React app will be served automatically by the static middleware above

async function main() {
    DBOS.setConfig({
        "name": "forachat",
        "databaseUrl": process.env.DBOS_DATABASE_URL,
        "userDbclient": "knex"
    });
    await DBOS.launch({ expressApp: app });
    console.log("DBOS Launched.");

    const port = 3000;
    server.listen(port, () => {
        console.log(`🚀 Server is running on http://localhost:${port}`);
        console.log(`🔌 WebSocket server is running on ws://localhost:${port}`);
    });
}

main().catch((error) => {
    console.error('❌ Server failed to start:', error);
    process.exit(1);
});
