import { DBOS } from '@dbos-inc/dbos-sdk';
import { ForaChat } from './operations';
import WebSocket from 'ws';

interface DelayedMessage {
  character: string;
  text: string;
  delay: number;
}

interface ChatResponse {
  reply: DelayedMessage[];
  theme?: string;
  skills?: string[];
  error?: string;
  details?: string;
  conversationId?: number;
}

interface StreamingSession {
  ws: WebSocket;
  isStreaming: boolean;
  timeouts: NodeJS.Timeout[];
  interrupted: boolean;
  pendingMessages: DelayedMessage[];
  conversationId?: number;
  lastMessageId?: number;
  pollInterval?: NodeJS.Timeout;
  autonomousTimer?: NodeJS.Timeout;
  extendedWorkflowTimer?: NodeJS.Timeout;
  extendedWorkflowActive: boolean;
  extendedWorkflowStartTime?: number;
  extendedWorkflowCharacterTimers: NodeJS.Timeout[];
}

export class StreamingChatService {
  private sessions: Map<string, StreamingSession> = new Map();

  constructor() {
    // Existing constructor code
  }

  createSession(sessionId: string, ws: WebSocket): void {
    const session: StreamingSession = {
      ws,
      isStreaming: false,
      timeouts: [],
      interrupted: false,
      pendingMessages: [],
      extendedWorkflowActive: false,
      extendedWorkflowCharacterTimers: []
    };

    this.sessions.set(sessionId, session);

    ws.on('message', async (data) => {
      try {
        const message = JSON.parse(data.toString());
        await this.handleMessage(sessionId, message);
      } catch (error) {
        this.sendError(sessionId, 'Invalid message format');
      }
    });

    ws.on('close', () => {
      this.cleanupSession(sessionId);
    });

    // Send welcome message
    this.sendMessage(sessionId, {
      type: 'connected',
      sessionId
    });
  }

  private async handleMessage(sessionId: string, message: any): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    switch (message.type) {
      case 'chat':
        await this.handleChatMessage(sessionId, message.text);
        break;
      case 'interrupt':
        this.handleInterrupt(sessionId, message.text);
        break;
      case 'start_extended_workflow':
        this.triggerExtendedWorkflow(sessionId);
        break;
      case 'get_extended_workflow_status':
        const status = this.getExtendedWorkflowStatus(sessionId);
        this.sendMessage(sessionId, {
          type: 'extended_workflow_status',
          ...status
        });
        break;
      default:
        this.sendError(sessionId, 'Unknown message type');
    }
  }

  private async handleChatMessage(sessionId: string, text: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    // If extended workflow is active, extend the timeout due to user activity
    if (session.extendedWorkflowActive) {
      this.extendWorkflowTimeout(sessionId);
    }

    if (session.isStreaming) {
      // User interrupted while messages are being streamed
      this.handleInterrupt(sessionId, text);
      return;
    }

    try {
      // If we have a conversation ID, check if the message is related
      let isRelated = true;
      if (session.conversationId) {
        DBOS.logger.info(`=== STREAMING CHAT MESSAGE PROCESSING ===`);
        DBOS.logger.info(`Session ID: ${sessionId}`);
        DBOS.logger.info(`Existing Conversation ID: ${session.conversationId}`);
        DBOS.logger.info(`User Message: "${text}"`);
        DBOS.logger.info(`Checking conversation relevance...`);

        const handle = await DBOS.startWorkflow(ForaChat).determineConversationRelevance(text, session.conversationId);
        isRelated = await handle.getResult();

        DBOS.logger.info(`=== CONVERSATION ROUTING DECISION ===`);
        DBOS.logger.info(`Message is related to existing conversation: ${isRelated}`);
        DBOS.logger.info(`Action: ${isRelated ? `Continue conversation ${session.conversationId}` : 'Start new conversation'}`);
      } else {
        DBOS.logger.info(`=== STREAMING CHAT MESSAGE PROCESSING ===`);
        DBOS.logger.info(`Session ID: ${sessionId}`);
        DBOS.logger.info(`No existing conversation - starting new conversation`);
        DBOS.logger.info(`User Message: "${text}"`);
      }

      // Start the chat workflow
      const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text, isRelated ? session.conversationId : undefined);
      const result: ChatResponse = await handle.getResult();

      if (result.error) {
        this.sendError(sessionId, result.error, result.details);
        return;
      }

      if (result.reply && Array.isArray(result.reply)) {
        // Store conversation ID for future interactions
        session.conversationId = result.conversationId;

        DBOS.logger.info(`=== CHAT WORKFLOW COMPLETED ===`);
        DBOS.logger.info(`Final Conversation ID: ${result.conversationId}`);
        DBOS.logger.info(`Response Messages: ${result.reply.length}`);
        DBOS.logger.info(`Theme: ${result.theme || 'Not set'}`);
        DBOS.logger.info(`Skills: ${result.skills ? JSON.stringify(result.skills) : 'Not set'}`);
        DBOS.logger.info(`Session will continue with conversation ID: ${session.conversationId}`);
        DBOS.logger.info(`=======================================`);

        this.sendMessage(sessionId, {
          type: 'chat_start',
          theme: result.theme || 'Chat Response'
        });

        await this.streamDelayedMessages(sessionId, result.reply, result.skills);

        // Start polling for delayed character thoughts
        this.pollForDelayedThoughts(sessionId);

        // Schedule autonomous character interactions if user is inactive
        this.scheduleAutonomousInteraction(sessionId);
      } else {
        this.sendError(sessionId, 'Invalid response format from LLM');
      }
    } catch (error) {
      this.sendError(sessionId, 'Failed to process chat message', (error as Error).message);
    }
  }

  private async handleInterrupt(sessionId: string, newText: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const wasExtendedWorkflowActive = session.extendedWorkflowActive;

    session.interrupted = true;
    this.clearTimeouts(sessionId);

    // Send appropriate interruption message
    const interruptMessage = wasExtendedWorkflowActive
      ? 'Extended conversation interrupted. Processing your new message...'
      : 'Processing your new message...';

    this.sendMessage(sessionId, {
      type: 'interrupted',
      message: interruptMessage,
      extendedWorkflowInterrupted: wasExtendedWorkflowActive
    });

    // Get the messages that were already sent
    const sentMessages: DelayedMessage[] = [];

    // Calculate which messages were already sent based on timing
    let cumulativeDelay = 0;

    for (const message of session.pendingMessages) {
      cumulativeDelay += (message.delay || 0);
      // If enough time has passed for this message to be sent, include it
      if (cumulativeDelay <= 1000) { // Rough estimate - in real implementation would track start time
        sentMessages.push(message);
      } else {
        break;
      }
    }

    try {
      // Use the interrupted chat workflow with context
      const handle = await DBOS.startWorkflow(ForaChat).interruptedChatWorkflow(
        newText,
        sentMessages.map(msg => ({ character: msg.character, text: msg.text })),
        session.conversationId
      );
      const result: ChatResponse = await handle.getResult();

      if (result.error) {
        this.sendError(sessionId, result.error, result.details);
        return;
      }

      if (result.reply && Array.isArray(result.reply)) {
        this.sendMessage(sessionId, {
          type: 'chat_start',
          theme: result.theme || 'Chat Response (Interrupted)'
        });

        await this.streamDelayedMessages(sessionId, result.reply, result.skills);

        // Start polling for delayed character thoughts for interrupted messages too
        this.pollForDelayedThoughts(sessionId);
      } else {
        this.sendError(sessionId, 'Invalid response format from LLM');
      }
    } catch (error) {
      this.sendError(sessionId, 'Failed to process interrupted message', (error as Error).message);
    }
  }

  private async streamDelayedMessages(
    sessionId: string, 
    messages: DelayedMessage[], 
    skills?: string[]
  ): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.isStreaming = true;
    session.interrupted = false;
    session.pendingMessages = [...messages];

    let cumulativeDelay = 0;

    messages.forEach((message, index) => {
      const delay = message.delay || 0;
      cumulativeDelay += delay; // Delay is already in milliseconds

      const timeout = setTimeout(() => {
        if (session.interrupted) {
          return; // Don't send if interrupted
        }

        this.sendMessage(sessionId, {
          type: 'message',
          character: message.character,
          text: message.text,
          index,
          total: messages.length
        });

        // If this is the last message, finish the stream
        if (index === messages.length - 1) {
          setTimeout(() => {
            if (!session.interrupted) {
              this.sendMessage(sessionId, {
                type: 'chat_complete',
                skills: skills || []
              });
              session.isStreaming = false;
              session.pendingMessages = [];

              // Start the extended 10-minute workflow
              this.startExtendedWorkflow(sessionId);
            }
          }, 100);
        }
      }, cumulativeDelay);

      session.timeouts.push(timeout);
    });
  }

  private clearTimeouts(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.timeouts.forEach(timeout => clearTimeout(timeout));
    session.timeouts = [];

    // Clear extended workflow timers
    if (session.extendedWorkflowTimer) {
      clearTimeout(session.extendedWorkflowTimer);
      session.extendedWorkflowTimer = undefined;
    }

    session.extendedWorkflowCharacterTimers.forEach(timeout => clearTimeout(timeout));
    session.extendedWorkflowCharacterTimers = [];
    session.extendedWorkflowActive = false;
  }

  private sendMessage(sessionId: string, message: any): void {
    const session = this.sessions.get(sessionId);
    if (!session || session.ws.readyState !== WebSocket.OPEN) return;

    session.ws.send(JSON.stringify(message));
  }

  private sendError(sessionId: string, error: string, details?: string): void {
    this.sendMessage(sessionId, {
      type: 'error',
      error,
      details
    });
  }

  private cleanupSession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      this.clearTimeouts(sessionId);

      // Clear polling interval
      if (session.pollInterval) {
        clearInterval(session.pollInterval);
      }

      // Clear autonomous timer
      if (session.autonomousTimer) {
        clearTimeout(session.autonomousTimer);
      }

      this.sessions.delete(sessionId);
    }
  }

  getSessionCount(): number {
    return this.sessions.size;
  }

  // Add a method to poll for and handle delayed character thoughts
  async pollForDelayedThoughts(sessionId: string) {
    const session = this.sessions.get(sessionId);
    if (!session || !session.conversationId) return;

    // Clear any existing polling interval to avoid duplicates
    if (session.pollInterval) {
      clearInterval(session.pollInterval);
    }

    let lastMessageId = session.lastMessageId || 0;
    
    // Set up polling interval (every 5 seconds)
    const pollInterval = setInterval(async () => {
      try {
        // Only check if the session is still active
        if (!session || !session.ws || session.ws.readyState !== WebSocket.OPEN) {
          clearInterval(pollInterval);
          return;
        }

        // Skip polling if currently streaming, but don't clear the interval
        if (session.isStreaming) {
          return;
        }
        
        const delayedMessages = await ForaChat.getDelayedThoughts(session.conversationId!, lastMessageId);

        DBOS.logger.info(`Polling for delayed thoughts - Conversation: ${session.conversationId}, LastMessageId: ${lastMessageId}, Found: ${delayedMessages.length} messages`);

        if (delayedMessages.length > 0) {
          // Update the last message ID
          lastMessageId = delayedMessages[delayedMessages.length - 1].id;
          session.lastMessageId = lastMessageId;

          DBOS.logger.info(`New delayed thoughts found: ${delayedMessages.map(m => `${m.character}: ${m.text.substring(0, 50)}...`).join(', ')}`);

          // Send a notification that new thoughts are available
          this.sendMessage(sessionId, {
            type: 'delayed_thoughts_available',
            count: delayedMessages.length
          });

          // Stream the delayed thoughts with a delay between each
          for (const message of delayedMessages) {
            await new Promise(resolve => setTimeout(resolve, 1500));
            this.sendMessage(sessionId, {
              type: 'delayed_thought',
              character: message.character,
              text: message.text
            });
          }
        }
      } catch (error) {
        DBOS.logger.error(`Error polling for delayed thoughts: ${(error as Error).message}`);
      }
    }, 5000);
    
    // Store the interval ID so we can clear it when the session ends
    session.pollInterval = pollInterval;
  }

  // Add a method to handle delayed character thoughts
  async handleDelayedCharacterThought(sessionId: string, message: DelayedMessage) {
    const session = this.sessions.get(sessionId);
    if (!session || !session.ws || session.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    // Add a longer delay for background thoughts (5-15 seconds)
    const thoughtDelay = 5000 + Math.random() * 10000;
    
    setTimeout(() => {
      // Only send if the session is still active and not in the middle of streaming
      if (session && !session.isStreaming) {
        this.sendMessage(sessionId, {
          type: 'delayed_thought',
          character: message.character,
          text: message.text
        });
      }
    }, thoughtDelay);
  }

  // Update the startChat method to initialize polling for delayed thoughts
  async startChat(sessionId: string, userMessage: string, conversationId?: number): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    // Set the conversation ID if provided
    if (conversationId) {
      session.conversationId = conversationId;
    }

    try {
      // Start the chat workflow
      const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(userMessage);
      const response: ChatResponse = await handle.getResult();

      if (response.error) {
        this.sendError(sessionId, response.error, response.details);
        return;
      }

      // After the chat response is processed, start polling for delayed thoughts
      if (response && response.conversationId) {
        session.conversationId = response.conversationId;
        this.pollForDelayedThoughts(sessionId);
      }

      if (response.reply && Array.isArray(response.reply)) {
        this.sendMessage(sessionId, {
          type: 'chat_start',
          theme: response.theme || 'Chat Response'
        });

        await this.streamDelayedMessages(sessionId, response.reply, response.skills);
      } else {
        this.sendError(sessionId, 'Invalid response format from LLM');
      }
    } catch (error) {
      this.sendError(sessionId, 'Failed to process chat message', (error as Error).message);
    }
  }

  // Update the closeSession method to clean up polling interval
  closeSession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      // Clear all timeouts
      for (const timeout of session.timeouts) {
        clearTimeout(timeout);
      }
      
      // Clear polling interval if it exists
      if (session.pollInterval) {
        clearInterval(session.pollInterval);
      }
      
      // Close WebSocket if it's open
      if (session.ws && session.ws.readyState === WebSocket.OPEN) {
        session.ws.close();
      }
      
      this.sessions.delete(sessionId);
    }
  }

  private scheduleAutonomousInteraction(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session || !session.conversationId) return;
  
    // Clear any existing autonomous interaction timer
    if (session.autonomousTimer) {
      clearTimeout(session.autonomousTimer);
    }
  
    // Set a timer for autonomous interaction (e.g., 30 seconds of inactivity)
    session.autonomousTimer = setTimeout(async () => {
      // Only proceed if the session is still active and not streaming
      if (!session || !session.ws || session.ws.readyState !== WebSocket.OPEN || session.isStreaming) {
        return;
      }
      
      try {
        // Get recent messages to use as context
        const recentMessages = await ForaChat.getDelayedThoughts(session.conversationId!);
        if (recentMessages.length === 0) return;
        
        // Choose a random character to continue the conversation
        const characters = ['Fora', 'Jan', 'Lou'];
        const character = characters[Math.floor(Math.random() * characters.length)];
        
        // Build context from recent messages
        const context = recentMessages.slice(-5).map(msg => `${msg.character}: ${msg.text}`).join('\n');
        
        // Generate an autonomous thought
        const handle = await DBOS.startWorkflow(ForaChat).characterThoughtWorkflow(
          session.conversationId!,
          context,
          character
        );
        
        const result = await handle.getResult();
        if (result && result.text) {
          // Send the autonomous message
          this.sendMessage(sessionId, {
            type: 'autonomous_message',
            character: result.character,
            text: result.text
          });
          
          // Schedule another autonomous interaction
          this.scheduleAutonomousInteraction(sessionId);
        }
      } catch (error) {
        console.error('Error in autonomous interaction:', error);
      }
    }, 30000); // 30 seconds of inactivity
  }

  // Start the extended 10-minute workflow where characters continue to chime in
  private startExtendedWorkflow(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session || !session.conversationId) return;

    // Clear any existing autonomous timer since we're starting extended workflow
    if (session.autonomousTimer) {
      clearTimeout(session.autonomousTimer);
      session.autonomousTimer = undefined;
    }

    session.extendedWorkflowActive = true;
    session.extendedWorkflowStartTime = Date.now();

    // Notify the client that extended workflow has started
    this.sendMessage(sessionId, {
      type: 'extended_workflow_start',
      duration: 600000, // 10 minutes in milliseconds
      message: 'Characters will continue the conversation for the next 10 minutes...'
    });

    // Set the main timer for 10 minutes
    session.extendedWorkflowTimer = setTimeout(() => {
      this.endExtendedWorkflow(sessionId);
    }, 600000); // 10 minutes

    // Schedule the first character interaction
    this.scheduleNextCharacterInteraction(sessionId);
  }

  // Schedule the next character interaction during extended workflow
  private scheduleNextCharacterInteraction(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session || !session.extendedWorkflowActive || !session.conversationId) return;

    // Random delay between 15-45 seconds for varied pacing
    const delay = 15000 + Math.random() * 30000;

    const timer = setTimeout(async () => {
      // Check if session is still active and extended workflow is running
      if (!session || !session.extendedWorkflowActive || !session.ws ||
          session.ws.readyState !== WebSocket.OPEN || session.isStreaming) {
        return;
      }

      try {
        // Get recent messages for context
        const recentMessages = await ForaChat.getDelayedThoughts(session.conversationId!);
        if (recentMessages.length === 0) {
          // Schedule next interaction even if no messages
          this.scheduleNextCharacterInteraction(sessionId);
          return;
        }

        // Choose a random character
        const characters = ['Fora', 'Jan', 'Lou'];
        const character = characters[Math.floor(Math.random() * characters.length)];

        // Build context from recent messages
        const context = recentMessages.slice(-5).map(msg => `${msg.character}: ${msg.text}`).join('\n');

        // Generate character thought
        const handle = await DBOS.startWorkflow(ForaChat).characterThoughtWorkflow(
          session.conversationId!,
          context,
          character
        );

        const result = await handle.getResult();
        if (result && result.text) {
          // Send the extended workflow message
          this.sendMessage(sessionId, {
            type: 'extended_workflow_message',
            character: result.character,
            text: result.text,
            timeRemaining: this.getExtendedWorkflowTimeRemaining(sessionId)
          });

          // Schedule the next interaction
          this.scheduleNextCharacterInteraction(sessionId);
        } else {
          // If no result, still schedule next interaction
          this.scheduleNextCharacterInteraction(sessionId);
        }
      } catch (error) {
        console.error('Error in extended workflow character interaction:', error);
        // Schedule next interaction even on error
        this.scheduleNextCharacterInteraction(sessionId);
      }
    }, delay);

    session.extendedWorkflowCharacterTimers.push(timer);
  }

  // End the extended workflow
  private endExtendedWorkflow(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.extendedWorkflowActive = false;

    // Clear all extended workflow timers
    if (session.extendedWorkflowTimer) {
      clearTimeout(session.extendedWorkflowTimer);
      session.extendedWorkflowTimer = undefined;
    }

    session.extendedWorkflowCharacterTimers.forEach(timer => clearTimeout(timer));
    session.extendedWorkflowCharacterTimers = [];

    // Notify the client that extended workflow has ended
    this.sendMessage(sessionId, {
      type: 'extended_workflow_end',
      message: 'Extended conversation period has ended.'
    });

    // Resume normal autonomous interaction scheduling
    this.scheduleAutonomousInteraction(sessionId);
  }

  // Get remaining time in extended workflow
  private getExtendedWorkflowTimeRemaining(sessionId: string): number {
    const session = this.sessions.get(sessionId);
    if (!session || !session.extendedWorkflowActive || !session.extendedWorkflowStartTime) {
      return 0;
    }

    const elapsed = Date.now() - session.extendedWorkflowStartTime;
    const remaining = Math.max(0, 600000 - elapsed); // 10 minutes - elapsed
    return remaining;
  }

  // Extend the workflow timeout when user is active
  private extendWorkflowTimeout(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session || !session.extendedWorkflowActive) return;

    // Clear the existing main timer
    if (session.extendedWorkflowTimer) {
      clearTimeout(session.extendedWorkflowTimer);
    }

    // Reset the start time to now (effectively extending by 10 minutes from now)
    session.extendedWorkflowStartTime = Date.now();

    // Set a new 10-minute timer
    session.extendedWorkflowTimer = setTimeout(() => {
      this.endExtendedWorkflow(sessionId);
    }, 600000); // 10 minutes

    // Notify the client that the workflow has been extended
    this.sendMessage(sessionId, {
      type: 'extended_workflow_extended',
      message: 'Extended conversation period renewed for another 10 minutes due to your activity!',
      newDuration: 600000,
      timeRemaining: 600000
    });
  }

  // Public method to manually start extended workflow (for testing or manual triggers)
  public triggerExtendedWorkflow(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session || !session.conversationId || session.extendedWorkflowActive) {
      return false;
    }

    this.startExtendedWorkflow(sessionId);
    return true;
  }

  // Public method to get extended workflow status
  public getExtendedWorkflowStatus(sessionId: string): {
    active: boolean;
    timeRemaining: number;
    startTime?: number;
  } {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return { active: false, timeRemaining: 0 };
    }

    return {
      active: session.extendedWorkflowActive,
      timeRemaining: this.getExtendedWorkflowTimeRemaining(sessionId),
      startTime: session.extendedWorkflowStartTime
    };
  }
}
